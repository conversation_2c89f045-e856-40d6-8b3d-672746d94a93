import { TRPCError } from "@trpc/server";
import { and, eq, inArray } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { validateShotputBody } from "~/server/api/utils/bodyValidation";
import { checkToken } from "~/server/api/utils/permissions";
import { returnError } from "~/server/api/utils/returnError";
import { getUserIdByToken } from "~/server/api/utils/service";
import { db } from "~/server/db";
import {
  bodyAngles,
  bodyKeypoints,
  shotputTags,
  shotputThrows,
} from "~/server/db/schema";

/**
 * @swagger
 * /api/v1/shotput/{id}:
 *   get:
 *     description: Get shotput throws with keypoints and angles for tagged frames.
 *     tags: [Shotput]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: number
 *                   videoId:
 *                     type: string
 *                   athleteId:
 *                     type: string
 *                   number:
 *                     type: number
 *                   movement:
 *                     type: string
 *                     enum: [ROTATIONAL, GLIDE]
 *                   type:
 *                     type: string
 *                     enum: [TRAINING, COMPETITION]
 *                   hand:
 *                     type: string
 *                     enum: [RIGHT, LEFT]
 *                   userId:
 *                     type: string
 *                   phases:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: number
 *                         throwId:
 *                           type: number
 *                         tag:
 *                           type: string
 *                           enum: [START, ENTRY, FLIGHT, DELIVERY, RELEASE, RECOVERY]
 *                         frame:
 *                           type: number
 *                         aiFrame:
 *                           type: number
 *                         userId:
 *                           type: string
 *                         keypoints:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               frameNumber:
 *                                 type: number
 *                               keypointNum:
 *                                 type: number
 *                               x:
 *                                 type: number
 *                               y:
 *                                 type: number
 *                               z:
 *                                 type: number
 *                               aiScore:
 *                                 type: number
 *                         angles:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               frameNumber:
 *                                 type: number
 *                               name:
 *                                 type: string
 *                               angle:
 *                                 type: number
 *                               aiScore:
 *                                 type: number
 *       400:
 *         description: Bad Request - Invalid video ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: BAD_REQUEST
 *                 message:
 *                   type: string
 *                   example: Invalid video id
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: UNAUTHORIZED
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: INTERNAL_SERVER_ERROR
 *                 message:
 *                   type: string
 *                   example: An unexpected error occurred
 *
 *   post:
 *     description: Add shotput tags to video.
 *     tags: [Shotput]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [throws]
 *             properties:
 *               throws:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required: [athleteId, number, movement, type, hand, tags]
 *                   properties:
 *                     athleteId:
 *                       type: string
 *                     number:
 *                       type: number
 *                     movement:
 *                       type: string
 *                       enum: [ROTATIONAL, GLIDE]
 *                     type:
 *                       type: string
 *                       enum: [TRAINING, COMPETITION]
 *                     hand:
 *                       type: string
 *                       enum: [RIGHT, LEFT]
 *                     tags:
 *                       type: array
 *                       items:
 *                         type: object
 *                         required: [phase, frame]
 *                         properties:
 *                           phase:
 *                             type: string
 *                             enum: [START, ENTRY, FLIGHT, DELIVERY, RELEASE, RECOVERY]
 *                           frame:
 *                             type: number
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Bad Request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: BAD_REQUEST
 *                 message:
 *                   type: string
 *                   example: Invalid request body
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: UNAUTHORIZED
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: INTERNAL_SERVER_ERROR
 *                 message:
 *                   type: string
 *                   example: An unexpected error occurred
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    await checkToken(request);

    const throws = await db.query.shotputThrows.findMany({
      where: eq(shotputThrows.videoId, id),
      with: {
        phases: true,
      },
    });
    const allTagFrames = throws.flatMap((x) => x.phases.map((y) => y.frame));

    const keypoints = await db.query.bodyKeypoints.findMany({
      where: and(
        eq(bodyKeypoints.videoId, id),
        inArray(bodyKeypoints.frameNumber, allTagFrames),
      ),
      columns: {
        frameNumber: true,
        keypointNum: true,
        x: true,
        y: true,
        z: true,
        aiScore: true,
      },
    });

    const angles = await db.query.bodyAngles.findMany({
      where: and(
        eq(bodyAngles.videoId, id),
        inArray(bodyAngles.frameNumber, allTagFrames),
      ),
      columns: {
        frameNumber: true,
        name: true,
        angle: true,
        aiScore: true,
      },
    });

    const throwsWithKeypoints = throws.map((x) => ({
      ...x,
      phases: x.phases.map((y) => ({
        ...y,
        keypoints: keypoints.filter((z) => z.frameNumber === y.frame),
        angles: angles.filter((z) => z.frameNumber === y.frame),
      })),
    }));

    return Response.json(throwsWithKeypoints);
  } catch (error) {
    return returnError(error);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    const { tokenPayload } = await checkToken(request);

    const userId = await getUserIdByToken(tokenPayload);

    const body = await validateShotputBody(request);

    for (const throwData of body.throws) {
      await db.transaction(async (trx) => {
        const result = await trx
          .insert(shotputThrows)
          .values({
            videoId: id,
            athleteId: throwData.athleteId,
            number: throwData.number,
            movement: throwData.movement,
            type: throwData.type,
            hand: throwData.hand,
            userId,
          })
          .$returningId();

        const throwId = result[0]?.id;
        if (!throwId) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to insert shotput throw",
          });
        }

        await trx.insert(shotputTags).values(
          throwData.tags.map((tag) => ({
            throwId,
            tag: tag.phase,
            frame: tag.frame,
            aiFrame: tag.frame,
            userId,
          })),
        );
      });
    }

    return Response.json({});
  } catch (cause) {
    return returnError(cause);
  }
}
