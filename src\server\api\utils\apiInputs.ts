import { z } from "zod";
import { LandingDescription } from "~/lib/enums/snow";

export const snowTagGeneralTagInputs = {
  id: z.string().optional(),
  startFrame: z.number(),
  endFrame: z.number(),
  jumpTypeId: z.number(),
  jumpTakeoffModifierId: z.number().nullish(),
  progression: z.boolean().optional(),
  switch: z.boolean().optional(),
  cab: z.boolean().optional(),
  spinDirection: z.string().nullish(),
  spinTypeId: z.number().nullish(),
  spinAmount: z.number().nullish(),
  spinModifierId: z.number().nullish(),
  grabTypeId: z.number().nullish(),
  grabStart: z.number().nullish(),
  grabEnd: z.number().nullish(),
  takeOffFrame: z.number().nullish(),
  landingFrame: z.number().nullish(),
  executionId: z.number().nullish(),
  landingZone: z.string().nullish(),
  landingType: z.string().nullish(),
  landingDescriptions: z.array(z.nativeEnum(LandingDescription)).nullish(),
};

export const snowTagGeneralInputs = {
  // general data
  videoId: z.string(),
  resultId: z.string(), // pta
  run: z.number(), //pta
  snowSportsRunId: z.string(), // get from pta, write to DB
  snowSportsFeatureId: z.string().optional(), // get from pta, write to DB
  //tag data
  ...snowTagGeneralTagInputs,
};

export const AngleSchema = z.object({
  frameNumber: z.number(),
  name: z.string(),
  angle: z.number(),
  aiScore: z.number(),
});

export const KeypointSchema = z.object({
  frameNumber: z.number(),
  keypointNum: z.number(),
  x: z.number(),
  y: z.number(),
  z: z.number(),
  aiScore: z.number(),
});

export const BodyDataSchema = z.object({
  angles: z.array(AngleSchema).optional(),
  keypoints: z.array(KeypointSchema).optional(),
});
