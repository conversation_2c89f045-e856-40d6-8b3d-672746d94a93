import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import type { RouterOutputs } from "~/trpc/react";
import { getPoseDataWithBuffering } from "../utils/poseData";

type Outputs = RouterOutputs["pose"];

export type GetPoseInfoOutput = Outputs["getAllPoseDataByVideo"];

export const poseRouter = createTRPCRouter({
  getAllPoseDataByVideo: protectedProcedure
    .input(
      z.object({
        videoId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      const { videoId } = input;

      return getPoseDataWithBuffering(videoId);
    }),
});
