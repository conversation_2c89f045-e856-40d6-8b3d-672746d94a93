import { and, eq, gte, lte } from "drizzle-orm";
import { bodyKeypoints, bodyAngles, shotputThrows } from "~/server/db/schema";
import { getVideoSummary } from "./video";
import { db } from "~/server/db";

export interface PoseDataWithBuffering {
  videoId: string;
  throw: {
    throwId: number;
    frameRange: {
      min: number;
      max: number;
      originalMin: number;
      originalMax: number;
    };
    keypoints: Array<{
      frameNumber: number;
      keypointNum: number;
      x: number;
      y: number;
    }>;
    angles: Array<{
      frameNumber: number;
      name: string;
      angle: number;
      aiScore: number;
    }>;
  } | null;
}

export async function getPoseDataWithBuffering(
  videoId: string,
): Promise<PoseDataWithBuffering> {
  // Get video info to access FPS
  const videoInfo = await getVideoSummary(videoId);
  const fps = videoInfo?.fps ?? 30; // Default to 30 fps if not available

  const throwData = await db.query.shotputThrows.findFirst({
    where: eq(shotputThrows.videoId, videoId),
    with: {
      phases: true,
    },
  });

  // If no throw found or no phases, return null
  if (!throwData?.phases?.length) {
    return {
      videoId,
      throw: null,
    };
  }

  const frames = throwData.phases.map(
    (phase: { frame: number }) => phase.frame,
  );
  const minFrame = Math.min(...frames);
  const maxFrame = Math.max(...frames);

  // Buffer by FPS (1 second before and after)
  const bufferedMinFrame = Math.max(0, minFrame - fps);
  const bufferedMaxFrame = maxFrame + fps;

  const [keypoints, angles] = await Promise.all([
    db.query.bodyKeypoints.findMany({
      where: and(
        eq(bodyKeypoints.videoId, videoId),
        gte(bodyKeypoints.frameNumber, bufferedMinFrame),
        lte(bodyKeypoints.frameNumber, bufferedMaxFrame),
      ),
      columns: {
        frameNumber: true,
        keypointNum: true,
        x: true,
        y: true,
        // Just in case we need in future
        // z: true,
        // aiScore: true,
      },
    }),

    db.query.bodyAngles.findMany({
      where: and(
        eq(bodyAngles.videoId, videoId),
        gte(bodyAngles.frameNumber, bufferedMinFrame),
        lte(bodyAngles.frameNumber, bufferedMaxFrame),
      ),
      columns: {
        frameNumber: true,
        name: true,
        angle: true,
        aiScore: true,
      },
    }),
  ]);

  return {
    videoId,
    throw: {
      throwId: throwData.id,
      frameRange: {
        min: bufferedMinFrame,
        max: bufferedMaxFrame,
        originalMin: minFrame,
        originalMax: maxFrame,
      },
      keypoints,
      angles,
    },
  };
}
